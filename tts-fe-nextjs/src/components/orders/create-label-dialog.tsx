'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useCreatePackage } from '@/lib/hooks';
import { Order } from '@/types/order';
import { Loader2 } from 'lucide-react';

// Form schema
const createLabelSchema = z.object({
  length: z.coerce.number().min(0.1, 'Length must be greater than 0'),
  width: z.coerce.number().min(0.1, 'Width must be greater than 0'),
  height: z.coerce.number().min(0.1, 'Height must be greater than 0'),
  dimensionUnit: z.string().min(1, 'Dimension unit is required'),
  weight: z.coerce.number().min(0.01, 'Weight must be greater than 0'),
  weightUnit: z.string().min(1, 'Weight unit is required'),
});

type CreateLabelFormData = z.infer<typeof createLabelSchema>;

interface CreateLabelDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: Order;
}

export function CreateLabelDialog({
  open,
  onOpenChange,
  order,
}: CreateLabelDialogProps) {
  const { mutate: createPackage, isPending: isCreating } = useCreatePackage();

  const form = useForm<CreateLabelFormData>({
    resolver: zodResolver(createLabelSchema),
    defaultValues: {
      length: 0,
      width: 0,
      height: 0,
      dimensionUnit: 'INCH',
      weight: 0,
      weightUnit: 'POUND',
    },
  });

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      form.reset({
        length: 0,
        width: 0,
        height: 0,
        dimensionUnit: 'INCH',
        weight: 0,
        weightUnit: 'POUND',
      });
    }
  }, [open, form]);

  const onSubmit = (data: CreateLabelFormData) => {
    if (!order.tiktokShop?.id || !order.idTT) {
      console.error('Missing required order data');
      return;
    }

    createPackage({
      tiktokShopId: order.tiktokShop.id,
      orderId: order.id,
      orderIdTT: order.idTT,
      dimension: {
        length: data.length,
        width: data.width,
        height: data.height,
        unit: data.dimensionUnit,
      },
      weight: {
        value: data.weight,
        unit: data.weightUnit,
      },
    }, {
      onSuccess: () => {
        onOpenChange(false);
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create Shipping Label</DialogTitle>
          <DialogDescription>
            Enter the package dimensions and weight to create a shipping label for this order.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Dimensions Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Package Dimensions</Label>
                <FormField
                  control={form.control}
                  name="dimensionUnit"
                  render={({ field }) => (
                    <FormItem>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="INCH">IN</SelectItem>
                          <SelectItem value="CM">CM</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-3 gap-3">
                <FormField
                  control={form.control}
                  name="length"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Length</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.1"
                          min="0.1"
                          placeholder="0.0"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="width"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Width</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.1"
                          min="0.1"
                          placeholder="0.0"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Height</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.1"
                          min="0.1"
                          placeholder="0.0"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Weight Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Package Weight</Label>
                <FormField
                  control={form.control}
                  name="weightUnit"
                  render={({ field }) => (
                    <FormItem>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="POUND">LB</SelectItem>
                          <SelectItem value="KG">KG</SelectItem>
                          <SelectItem value="OUNCE">OZ</SelectItem>
                          <SelectItem value="GRAM">G</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="weight"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Weight</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0.01"
                        placeholder="0.00"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isCreating}>
                {isCreating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Label
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
