import { createPackage } from '../order-service';
import { apiRequest } from '../../api-client';
import { CreatePackageDto } from '@/types/order';

// Mock the apiRequest function
jest.mock('../../api-client', () => ({
  apiRequest: jest.fn(),
}));

const mockedApiRequest = apiRequest as jest.MockedFunction<typeof apiRequest>;

describe('Order Service - createPackage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call createPackage API with correct parameters', async () => {
    const mockResponse = {
      id: 1,
      packageIdTT: '1154528977690660930',
      orderIdTT: '7891234567890123456',
      orderId: 123,
      orderLineItemIds: ['7123456789', '7123456790'],
      createTimeTT: 1640995200,
      shippingServiceInfo: { id: 'standard_shipping', name: 'Standard Shipping' },
      dimension: { length: 10, width: 8, height: 5, unit: 'INCH' },
      weight: { value: 1.5, unit: 'POUND' },
      createdAt: new Date(),
      updatedAt: new Date(),
      isNew: true,
    };

    mockedApiRequest.mockResolvedValue(mockResponse);

    const createPackageData: CreatePackageDto = {
      tiktokShopId: 1,
      orderId: 123,
      orderIdTT: '7891234567890123456',
      dimension: {
        length: 10,
        width: 8,
        height: 5,
        unit: 'INCH',
      },
      weight: {
        value: 1.5,
        unit: 'POUND',
      },
    };

    const result = await createPackage(createPackageData, 'test-token');

    expect(mockedApiRequest).toHaveBeenCalledWith({
      url: '/orders/create-package',
      method: 'POST',
      data: createPackageData,
      token: 'test-token',
    });

    expect(result).toEqual(mockResponse);
  });

  it('should handle API errors correctly', async () => {
    const mockError = new Error('API Error');
    mockedApiRequest.mockRejectedValue(mockError);

    const createPackageData: CreatePackageDto = {
      tiktokShopId: 1,
      orderId: 123,
      orderIdTT: '7891234567890123456',
      dimension: {
        length: 10,
        width: 8,
        height: 5,
        unit: 'INCH',
      },
      weight: {
        value: 1.5,
        unit: 'POUND',
      },
    };

    await expect(createPackage(createPackageData, 'test-token')).rejects.toThrow('API Error');
  });
});
